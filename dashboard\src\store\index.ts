import { persistStore, persistReducer, FLUSH, REHY<PERSON><PERSON><PERSON>, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import { configureStore, combineReducers } from '@reduxjs/toolkit'


const rootPresistConfig = {
  key: "root",
  storage,
  whitelist: ["cart", "auth"],
}

const authPresistConfig = {
  key: "auth",
  storage,
  whitelist: ["accessToken", "user"],
}

const cartPresistConfig = {
  key: "cart",
  storage,
  whitelist: ["items"],
}


const rootReducer = combineReducers({
    auth: persistReducer(authPresistConfig, auth),
    categories,
    products,
    order,
    cart: persistReducer(cartPresistConfig, cart) ,
    wishlist,
  })

const persistedReducer = persistReducer(rootPresistConfig, rootReducer)  

const store = configureStore({
  reducer: persistedReducer,
  devTools: true,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware({
    serializableCheck: {
      ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
    },
  }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

const persistor = persistStore(store)

export {store, persistor}
